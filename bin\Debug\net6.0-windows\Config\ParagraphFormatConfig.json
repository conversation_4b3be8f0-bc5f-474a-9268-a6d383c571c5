{
  // 段落格式配置文件 - 控制PPT段落对齐、缩进、行距等格式设置
  // 此文件定义了段落对齐方式、缩进距离、行距、间距等格式参数，支持条件匹配和格式应用

  // 启用段落格式匹配功能开关 - 是否启用段落格式匹配功能
  "EnableParagraphFormatMatching": false,

  // 匹配规则列表 - 定义段落格式匹配的具体规则
  "MatchingRules": [
    {
      // 启用此规则 - 是否启用当前匹配规则
      "IsEnabled": true,
      // 规则名称 - 匹配规则的名称，便于识别和管理
      "RuleName": "示例规则",

      // 匹配条件设置 - 定义段落文本的匹配条件
      "MatchingConditions": {
        // 启用开头匹配 - 是否启用文本开头匹配条件
        "EnableStartsWith": false,
        // 开头文本 - 段落开头需要匹配的文本
        "StartsWithText": "",

        // 启用包含匹配 - 是否启用文本包含匹配条件
        "EnableContains": false,
        // 包含关键词列表 - 段落需要包含的关键词
        "ContainsKeywords": [],

        // 启用结尾匹配 - 是否启用文本结尾匹配条件
        "EnableEndsWith": false,
        // 结尾文本 - 段落结尾需要匹配的文本
        "EndsWithText": "",

        // 启用正则表达式匹配 - 是否启用正则表达式匹配条件
        "EnableRegex": false,
        // 正则表达式模式 - 用于匹配段落文本的正则表达式
        "RegexPattern": "",

        // 启用字符数限制 - 是否启用段落字符数限制条件
        "EnableCharacterCountLimit": false,
        // 最小字符数 - 段落文本的最小字符数
        "MinCharacterCount": 1,
        // 最大字符数 - 段落文本的最大字符数
        "MaxCharacterCount": 1000,

        // 区分大小写 - 文本匹配是否区分大小写
        "CaseSensitive": false,
        // 完整单词匹配 - 是否只匹配完整的单词
        "WholeWord": false
      },
      // 段落格式设置 - 定义要应用的段落格式
      "ParagraphFormat": {
        // 启用段落格式 - 是否启用段落格式设置
        "EnableParagraphFormat": false,

        // 对齐方式 - 段落对齐方式：0=左对齐，1=居中，2=右对齐，3=两端对齐
        "Alignment": 0,

        // 启用缩进设置 - 是否启用段落缩进设置
        "EnableIndentation": false,
        // 左缩进 - 段落左侧缩进距离
        "LeftIndent": 0.0,
        // 左边距 - 段落左边距
        "MarginLeft": 0.0,
        // 右边距 - 段落右边距
        "MarginRight": 0.0,
        // 特殊缩进类型 - 特殊缩进类型：0=无，1=首行缩进，2=悬挂缩进
        "SpecialIndent": 0,
        // 特殊缩进值 - 特殊缩进的距离
        "SpecialIndentValue": 0.0,

        // 启用间距设置 - 是否启用段落间距设置
        "EnableSpacing": false,
        // 段前间距 - 段落前的间距
        "SpaceBefore": 0.0,
        // 段后间距 - 段落后的间距
        "SpaceAfter": 0.0,
        // 行距类型 - 行距类型：0=单倍行距，1=1.5倍行距，2=双倍行距，3=最小值，4=固定值，5=多倍行距
        "LineSpacingType": 0,
        // 行距值 - 行距的具体数值
        "LineSpacingValue": 1.0,

        // 启用中文控制 - 是否启用中文排版控制
        "EnableChineseControl": false,
        // 中文字符控制 - 是否启用中文字符控制
        "ChineseCharacterControl": false,
        // 允许拉丁文换行 - 是否允许拉丁文单词在行末换行
        "AllowLatinWordBreak": false,
        // 允许标点溢出 - 是否允许标点符号溢出行边界
        "AllowPunctuationOverhang": false,

        // 文本对齐 - 文本在段落中的对齐方式：0=基线，1=顶部，2=中间，3=底部
        "TextAlignment": 0,

        // 启用字体对齐 - 是否启用字体对齐设置
        "EnableFontAlignment": false,
        // 字体对齐 - 字体对齐方式：0=自动，1=顶部，2=中心，3=基线，4=底部
        "FontAlignment": 0,

        // 启用从右到左 - 是否启用从右到左的文本方向
        "EnableRightToLeft": false,
        // 从右到左 - 是否使用从右到左的文本方向
        "RightToLeft": false
      },
      // 字体格式设置 - 定义要应用的字体格式
      "FontFormat": {
        // 启用字体格式 - 是否启用字体格式设置
        "EnableFontFormat": false,

        // 设置中文字体 - 是否设置中文字体
        "SetChineseFont": false,
        // 中文字体名称 - 中文字体的名称
        "ChineseFontName": "宋体",

        // 设置拉丁字体 - 是否设置拉丁字体
        "SetLatinFont": false,
        // 拉丁字体名称 - 拉丁字体的名称
        "LatinFontName": "Arial",

        // 字体样式 - 字体样式：0=常规，1=粗体，2=斜体，3=粗斜体
        "FontStyle": 0,

        // 设置字体大小 - 是否设置字体大小
        "SetFontSize": false,
        // 字体大小 - 字体的大小（磅）
        "FontSize": 12.0,

        // 设置字体颜色 - 是否设置字体颜色
        "SetFontColor": false,
        // 字体颜色 - 字体的颜色（十六进制格式）
        "FontColor": "#000000",

        // 设置下划线 - 是否设置下划线
        "SetUnderline": false,
        // 下划线类型 - 下划线的类型：0=无，1=单下划线，2=双下划线，3=粗下划线
        "UnderlineType": 0,
        // 下划线颜色 - 下划线的颜色（十六进制格式）
        "UnderlineColor": "#000000",

        // 设置文本效果 - 是否设置文本效果
        "SetTextEffects": false,
        // 删除线 - 是否添加删除线效果
        "Strikethrough": false,
        // 双删除线 - 是否添加双删除线效果
        "DoubleStrikethrough": false,
        // 上标 - 是否设置为上标
        "Superscript": false,
        // 下标 - 是否设置为下标
        "Subscript": false
      },

      // 创建时间 - 规则的创建时间
      "CreatedTime": "2024-01-01T00:00:00",
      // 最后修改时间 - 规则的最后修改时间
      "LastModifiedTime": "2024-01-01T00:00:00"
    }
  ]
}
