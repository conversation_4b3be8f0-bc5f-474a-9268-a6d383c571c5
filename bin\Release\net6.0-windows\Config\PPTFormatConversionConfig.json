{
  // PPT格式转换配置文件 - 控制PPT转换为其他格式的设置
  // 此文件定义了PDF转换、图片转换、网页转换和其他格式转换的详细参数

  // PDF转换设置 - 将PPT转换为PDF格式的配置
  "PDFSettings": {
    // 启用PDF转换功能开关 - 是否启用PPT转PDF功能
    "EnablePDFConversion": false,
    // 批量转换 - 是否支持批量转换多个PPT文件
    "BatchConversion": true,
    // 保持原始格式 - 是否在转换时保持PPT的原始格式
    "KeepOriginalFormat": true,

    // 使用页面范围 - 是否只转换指定范围的幻灯片
    "UsePageRange": false,
    // 起始页 - 转换的起始幻灯片页码
    "StartPage": 1,
    // 结束页 - 转换的结束幻灯片页码
    "EndPage": 1,

    // 图片质量 - PDF中图片的质量（1-100）
    "ImageQuality": 90,
    // 压缩级别 - PDF文件的压缩程度：标准、高压缩、无压缩
    "CompressionLevel": "标准",

    // 密码保护 - 是否为生成的PDF设置密码保护
    "PasswordProtection": false,
    // 密码 - PDF文件的访问密码
    "Password": "",

    // 幻灯片布局 - PDF中幻灯片的布局方式
    "SlideLayout": "幻灯片布局",
    // 讲义多张幻灯片 - 是否在一页中显示多张幻灯片
    "HandoutMultipleSlides": false,
    // 每页幻灯片数 - 讲义模式下每页显示的幻灯片数量
    "HandoutSlidesPerPage": 6,

    // 包含备注 - 是否在PDF中包含幻灯片备注
    "IncludeNotes": false,
    // 备注位置 - 备注在PDF中的显示位置：底部、右侧
    "NotesPosition": "底部",

    // 嵌入字体 - 是否在PDF中嵌入字体文件
    "EmbedFonts": true,
    // 压缩图片 - 是否压缩PDF中的图片
    "CompressImages": true,
    // 保留元数据 - 是否保留原PPT的元数据信息
    "PreserveMetadata": true,
    // 生成书签 - 是否根据幻灯片标题生成PDF书签
    "GenerateBookmarks": false,
    // PDF兼容性 - 生成PDF的版本标准
    "PDFCompliance": "PDF 1.5"
  },

  // 图片转换设置 - 将PPT转换为图片格式的配置
  "ImageSettings": {
    // 启用PNG转换 - 是否启用PPT转PNG功能
    "EnablePNGConversion": false,
    // 启用JPEG转换 - 是否启用PPT转JPEG功能
    "EnableJPEGConversion": false,
    // 启用BMP转换 - 是否启用PPT转BMP功能
    "EnableBMPConversion": false,
    // 启用TIFF转换 - 是否启用PPT转TIFF功能
    "EnableTIFFConversion": false,
    // 启用SVG转换 - 是否启用PPT转SVG功能
    "EnableSVGConversion": false,

    // 图片宽度 - 生成图片的宽度（像素）
    "ImageWidth": 1920,
    // 图片高度 - 生成图片的高度（像素）
    "ImageHeight": 1080,
    // DPI - 图片的分辨率（每英寸点数）
    "DPI": 300,
    // JPEG质量 - JPEG格式的图片质量（1-100）
    "JPEGQuality": 90,

    // 保持宽高比 - 是否保持原幻灯片的宽高比
    "MaintainAspectRatio": true,
    // 转换所有幻灯片 - 是否转换PPT中的所有幻灯片
    "ConvertAllSlides": true,

    // 使用幻灯片范围 - 是否只转换指定范围的幻灯片
    "UseSlideRange": false,
    // 起始幻灯片 - 转换的起始幻灯片页码
    "StartSlide": 1,
    // 结束幻灯片 - 转换的结束幻灯片页码
    "EndSlide": 1,

    // 命名模式 - 生成图片文件的命名规则，{0}为幻灯片序号
    "NamingPattern": "幻灯片{0}"
  },

  // 网页转换设置 - 将PPT转换为网页格式的配置
  "WebSettings": {
    // 启用HTML转换 - 是否启用PPT转HTML功能
    "EnableHTMLConversion": false,
    // 嵌入图片 - 是否将图片嵌入到HTML文件中
    "EmbedImages": true,
    // 嵌入CSS - 是否将CSS样式嵌入到HTML文件中
    "EmbedCSS": true,
    // 嵌入JavaScript - 是否将JavaScript代码嵌入到HTML文件中
    "EmbedJavaScript": true,

    // HTML版本 - 生成HTML的版本标准
    "HTMLVersion": "HTML5",
    // 响应式设计 - 是否生成响应式的HTML页面
    "ResponsiveDesign": true,

    // 启用XAML转换 - 是否启用PPT转XAML功能
    "EnableXAMLConversion": false,
    // 包含动画 - 是否在转换后保留PPT中的动画效果
    "IncludeAnimations": false,
    // 为Silverlight优化 - 是否为Silverlight平台优化输出
    "OptimizeForSilverlight": false
  },

  // 其他转换设置 - 其他格式转换的配置
  "OtherSettings": {
    // 启用缩略图生成 - 是否生成PPT的缩略图
    "EnableThumbnailGeneration": false,
    // 缩略图宽度 - 生成缩略图的宽度（像素）
    "ThumbnailWidth": 200,
    // 缩略图高度 - 生成缩略图的高度（像素）
    "ThumbnailHeight": 150,
    // 缩略图格式 - 缩略图的图片格式：PNG、JPEG、BMP
    "ThumbnailFormat": "PNG",
    // 为所有幻灯片生成 - 是否为每张幻灯片都生成缩略图
    "GenerateForAllSlides": true
  }
}
