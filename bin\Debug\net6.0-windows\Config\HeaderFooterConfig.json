{
  // 页眉页脚配置文件 - 控制PPT页眉页脚的添加、修改和删除功能
  // 此文件定义了页眉页脚的内容、格式和显示设置，包括演示文稿设置、幻灯片设置、母版设置、版式设置和备注设置

  // 启用页眉页脚功能开关 - 是否启用页眉页脚处理功能
  "IsEnabled": false,

  // 删除设置 - 控制页眉页脚元素的删除操作
  "DeletionSettings": {
    // 删除所有页脚 - 删除PPT中的所有页脚文本
    "DeleteAllFooters": false,
    // 删除所有幻灯片编号 - 删除PPT中的所有幻灯片编号
    "DeleteAllSlideNumbers": false,
    // 删除所有日期时间 - 删除PPT中的所有日期时间信息
    "DeleteAllDateTimes": false,
    // 删除范围 - 删除操作的应用范围：All=所有幻灯片，Range=指定范围，Selected=选定幻灯片
    "DeletionScope": "All",
    // 幻灯片范围 - 当删除范围为Range时的幻灯片范围（如"1-5"表示第1到5张）
    "SlideRange": "1-",
    // 选定幻灯片索引 - 当删除范围为Selected时的幻灯片索引列表
    "SelectedSlideIndexes": []
  },

  // 演示文稿设置 - 控制整个演示文稿的页眉页脚设置
  "PresentationSettings": {
    // 启用演示文稿设置 - 是否启用演示文稿级别的页眉页脚设置
    "IsEnabled": false,
    // 页脚文本 - 演示文稿页脚的文本内容
    "FooterText": "",
    // 显示页脚 - 是否在演示文稿中显示页脚
    "ShowFooter": false,
    // 显示幻灯片编号 - 是否在演示文稿中显示幻灯片编号
    "ShowSlideNumber": false,
    // 幻灯片编号格式 - 幻灯片编号的显示格式（#代表编号）
    "SlideNumberFormat": "幻灯片 #",
    // 显示日期时间 - 是否在演示文稿中显示日期时间
    "ShowDateTime": false,
    // 日期时间文本 - 自定义的日期时间文本
    "DateTimeText": "",
    // 日期时间格式 - 日期时间的显示格式
    "DateTimeFormat": "yyyy/MM/dd",
    // 自动更新日期时间 - 是否自动更新日期时间为当前时间
    "AutoUpdateDateTime": true
  },
  // 幻灯片设置 - 控制普通幻灯片的页眉页脚设置
  "SlideSettings": {
    // 启用幻灯片设置 - 是否启用普通幻灯片的页眉页脚设置
    "IsEnabled": false,

    // 页脚设置 - 普通幻灯片的页脚配置
    "Footer": {
      // 启用页脚 - 是否启用页脚功能
      "IsEnabled": false,
      // 页脚文本 - 页脚显示的文本内容
      "Text": "",
      // 页脚可见 - 页脚是否在幻灯片上可见
      "IsVisible": false,
      // 应用到所有幻灯片 - 是否将页脚设置应用到所有幻灯片
      "ApplyToAllSlides": true
    },

    // 幻灯片编号设置 - 普通幻灯片的编号配置
    "SlideNumber": {
      // 启用幻灯片编号 - 是否启用幻灯片编号功能
      "IsEnabled": false,
      // 编号可见 - 幻灯片编号是否在幻灯片上可见
      "IsVisible": false,
      // 编号格式 - 幻灯片编号的显示格式（#代表编号）
      "Format": "#",
      // 编号前缀 - 幻灯片编号前面的文本
      "Prefix": "",
      // 编号后缀 - 幻灯片编号后面的文本
      "Suffix": "",
      // 起始编号 - 幻灯片编号的起始数字
      "StartNumber": 1
    },

    // 日期时间设置 - 普通幻灯片的日期时间配置
    "DateTime": {
      // 启用日期时间 - 是否启用日期时间功能
      "IsEnabled": false,
      // 日期时间可见 - 日期时间是否在幻灯片上可见
      "IsVisible": false,
      // 日期时间格式 - 日期时间的显示格式
      "Format": "yyyy/MM/dd",
      // 自定义文本 - 自定义的日期时间文本
      "CustomText": "",
      // 自动更新 - 是否自动更新为当前日期时间
      "AutoUpdate": true,
      // 使用自定义文本 - 是否使用自定义文本而不是格式化日期
      "UseCustomText": false
    },

    // 应用范围 - 设置应用的幻灯片范围：AllSlides=所有幻灯片，Range=指定范围，Selected=选定幻灯片
    "ApplyScope": "AllSlides",
    // 幻灯片范围 - 当应用范围为Range时的幻灯片范围
    "SlideRange": "1-",
    // 选定幻灯片索引 - 当应用范围为Selected时的幻灯片索引列表
    "SelectedSlideIndexes": []
  },
  // 母版幻灯片设置 - 控制母版幻灯片的页眉页脚设置
  "MasterSlideSettings": {
    // 启用母版设置 - 是否启用母版幻灯片的页眉页脚设置
    "IsEnabled": false,

    // 母版页脚设置 - 母版幻灯片的页脚配置
    "Footer": {
      // 启用页脚 - 是否启用母版页脚功能
      "IsEnabled": false,
      // 页脚文本 - 母版页脚显示的文本内容
      "Text": "",
      // 页脚可见 - 母版页脚是否可见
      "IsVisible": false,
      // 应用到所有幻灯片 - 是否将母版页脚设置应用到所有幻灯片
      "ApplyToAllSlides": true
    },

    // 母版幻灯片编号设置 - 母版幻灯片的编号配置
    "SlideNumber": {
      // 启用幻灯片编号 - 是否启用母版幻灯片编号功能
      "IsEnabled": false,
      // 编号可见 - 母版幻灯片编号是否可见
      "IsVisible": false,
      // 编号格式 - 母版幻灯片编号的显示格式
      "Format": "#",
      // 编号前缀 - 母版幻灯片编号前面的文本
      "Prefix": "",
      // 编号后缀 - 母版幻灯片编号后面的文本
      "Suffix": "",
      // 起始编号 - 母版幻灯片编号的起始数字
      "StartNumber": 1
    },

    // 母版日期时间设置 - 母版幻灯片的日期时间配置
    "DateTime": {
      // 启用日期时间 - 是否启用母版日期时间功能
      "IsEnabled": false,
      // 日期时间可见 - 母版日期时间是否可见
      "IsVisible": false,
      // 日期时间格式 - 母版日期时间的显示格式
      "Format": "yyyy/MM/dd",
      // 自定义文本 - 母版自定义的日期时间文本
      "CustomText": "",
      // 自动更新 - 是否自动更新母版日期时间
      "AutoUpdate": true,
      // 使用自定义文本 - 是否使用自定义文本而不是格式化日期
      "UseCustomText": false
    },

    // 应用到子幻灯片 - 是否将母版设置应用到基于此母版的子幻灯片
    "ApplyToChildSlides": true
  },
  // 版式幻灯片设置 - 控制版式幻灯片的页眉页脚设置
  "LayoutSlideSettings": {
    // 启用版式设置 - 是否启用版式幻灯片的页眉页脚设置
    "IsEnabled": false,

    // 版式页脚设置 - 版式幻灯片的页脚配置
    "Footer": {
      // 启用页脚 - 是否启用版式页脚功能
      "IsEnabled": false,
      // 页脚文本 - 版式页脚显示的文本内容
      "Text": "",
      // 页脚可见 - 版式页脚是否可见
      "IsVisible": false,
      // 应用到所有幻灯片 - 是否将版式页脚设置应用到所有幻灯片
      "ApplyToAllSlides": true
    },

    // 版式幻灯片编号设置 - 版式幻灯片的编号配置
    "SlideNumber": {
      // 启用幻灯片编号 - 是否启用版式幻灯片编号功能
      "IsEnabled": false,
      // 编号可见 - 版式幻灯片编号是否可见
      "IsVisible": false,
      // 编号格式 - 版式幻灯片编号的显示格式
      "Format": "#",
      // 编号前缀 - 版式幻灯片编号前面的文本
      "Prefix": "",
      // 编号后缀 - 版式幻灯片编号后面的文本
      "Suffix": "",
      // 起始编号 - 版式幻灯片编号的起始数字
      "StartNumber": 1
    },

    // 版式日期时间设置 - 版式幻灯片的日期时间配置
    "DateTime": {
      // 启用日期时间 - 是否启用版式日期时间功能
      "IsEnabled": false,
      // 日期时间可见 - 版式日期时间是否可见
      "IsVisible": false,
      // 日期时间格式 - 版式日期时间的显示格式
      "Format": "yyyy/MM/dd",
      // 自定义文本 - 版式自定义的日期时间文本
      "CustomText": "",
      // 自动更新 - 是否自动更新版式日期时间
      "AutoUpdate": true,
      // 使用自定义文本 - 是否使用自定义文本而不是格式化日期
      "UseCustomText": false
    },

    // 应用到所有版式 - 是否将设置应用到所有版式幻灯片
    "ApplyToAllLayouts": true,
    // 应用到子幻灯片 - 是否将版式设置应用到基于此版式的子幻灯片
    "ApplyToChildSlides": true,
    // 选定版式索引 - 当不应用到所有版式时，选定的版式索引列表
    "SelectedLayoutIndexes": []
  },
  // 备注幻灯片设置 - 控制备注幻灯片的页眉页脚设置
  "NotesSlideSettings": {
    // 启用备注设置 - 是否启用备注幻灯片的页眉页脚设置
    "IsEnabled": false,

    // 备注母版设置 - 控制备注母版的页眉页脚配置
    "NotesMaster": {
      // 启用备注母版设置 - 是否启用备注母版的页眉页脚设置
      "IsEnabled": false,

      // 备注母版页脚设置 - 备注母版的页脚配置
      "Footer": {
        // 启用页脚 - 是否启用备注母版页脚功能
        "IsEnabled": false,
        // 页脚文本 - 备注母版页脚显示的文本内容
        "Text": "",
        // 页脚可见 - 备注母版页脚是否可见
        "IsVisible": false,
        // 应用到所有幻灯片 - 是否将备注母版页脚设置应用到所有幻灯片
        "ApplyToAllSlides": true
      },

      // 备注母版幻灯片编号设置 - 备注母版的编号配置
      "SlideNumber": {
        // 启用幻灯片编号 - 是否启用备注母版幻灯片编号功能
        "IsEnabled": false,
        // 编号可见 - 备注母版幻灯片编号是否可见
        "IsVisible": false,
        // 编号格式 - 备注母版幻灯片编号的显示格式
        "Format": "#",
        // 编号前缀 - 备注母版幻灯片编号前面的文本
        "Prefix": "",
        // 编号后缀 - 备注母版幻灯片编号后面的文本
        "Suffix": "",
        // 起始编号 - 备注母版幻灯片编号的起始数字
        "StartNumber": 1
      },

      // 备注母版日期时间设置 - 备注母版的日期时间配置
      "DateTime": {
        // 启用日期时间 - 是否启用备注母版日期时间功能
        "IsEnabled": false,
        // 日期时间可见 - 备注母版日期时间是否可见
        "IsVisible": false,
        // 日期时间格式 - 备注母版日期时间的显示格式
        "Format": "yyyy/MM/dd",
        // 自定义文本 - 备注母版自定义的日期时间文本
        "CustomText": "",
        // 自动更新 - 是否自动更新备注母版日期时间
        "AutoUpdate": true,
        // 使用自定义文本 - 是否使用自定义文本而不是格式化日期
        "UseCustomText": false
      },

      // 应用到子幻灯片 - 是否将备注母版设置应用到基于此母版的子幻灯片
      "ApplyToChildSlides": true
    },

    // 备注幻灯片设置 - 控制普通备注幻灯片的页眉页脚配置
    "NotesSlide": {
      // 启用备注幻灯片设置 - 是否启用普通备注幻灯片的页眉页脚设置
      "IsEnabled": false,

      // 备注幻灯片页脚设置 - 普通备注幻灯片的页脚配置
      "Footer": {
        // 启用页脚 - 是否启用备注幻灯片页脚功能
        "IsEnabled": false,
        // 页脚文本 - 备注幻灯片页脚显示的文本内容
        "Text": "",
        // 页脚可见 - 备注幻灯片页脚是否可见
        "IsVisible": false,
        // 应用到所有幻灯片 - 是否将备注幻灯片页脚设置应用到所有幻灯片
        "ApplyToAllSlides": true
      },

      // 备注幻灯片编号设置 - 普通备注幻灯片的编号配置
      "SlideNumber": {
        // 启用幻灯片编号 - 是否启用备注幻灯片编号功能
        "IsEnabled": false,
        // 编号可见 - 备注幻灯片编号是否可见
        "IsVisible": false,
        // 编号格式 - 备注幻灯片编号的显示格式
        "Format": "#",
        // 编号前缀 - 备注幻灯片编号前面的文本
        "Prefix": "",
        // 编号后缀 - 备注幻灯片编号后面的文本
        "Suffix": "",
        // 起始编号 - 备注幻灯片编号的起始数字
        "StartNumber": 1
      },

      // 备注幻灯片日期时间设置 - 普通备注幻灯片的日期时间配置
      "DateTime": {
        // 启用日期时间 - 是否启用备注幻灯片日期时间功能
        "IsEnabled": false,
        // 日期时间可见 - 备注幻灯片日期时间是否可见
        "IsVisible": false,
        // 日期时间格式 - 备注幻灯片日期时间的显示格式
        "Format": "yyyy/MM/dd",
        // 自定义文本 - 备注幻灯片自定义的日期时间文本
        "CustomText": "",
        // 自动更新 - 是否自动更新备注幻灯片日期时间
        "AutoUpdate": true,
        // 使用自定义文本 - 是否使用自定义文本而不是格式化日期
        "UseCustomText": false
      },

      // 应用范围 - 备注幻灯片设置的应用范围：AllNotesSlides=所有备注幻灯片，Selected=选定备注幻灯片
      "ApplyScope": "AllNotesSlides",
      // 选定备注幻灯片索引 - 当应用范围为Selected时的备注幻灯片索引列表
      "SelectedNotesSlideIndexes": []
    }
  }
}
