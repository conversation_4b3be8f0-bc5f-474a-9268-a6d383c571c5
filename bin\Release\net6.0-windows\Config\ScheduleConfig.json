{
  // 定时处理配置文件 - 控制PPT批量处理的定时执行功能
  // 此文件定义了三种互斥的执行模式：一次性执行、定时执行、倒计时执行

  // 启用定时处理功能开关 - 是否启用定时处理功能
  "Enabled": false,

  // 定时模式 - 执行模式选择：0=一次性执行，1=定时执行，2=倒计时执行（三种模式互斥）
  "ScheduleMode": 0,

  // 一次性执行设置 - 在指定时间执行一次处理任务
  "OneTimeExecution": {
    // 执行时间 - 任务执行的具体日期和时间
    "ExecutionDateTime": "2024-01-01T00:00:00"
  },

  // 定时执行设置 - 按照指定的时间规律重复执行
  "ScheduledExecution": {
    // 重复类型 - 重复频率：0=每年，1=每月，2=每日，3=每小时
    "RecurrenceType": 0,
    // 年份 - 执行年份（当重复类型为每年时使用）
    "Year": 2024,
    // 月份 - 执行月份（当重复类型为每年或每月时使用）
    "Month": 1,
    // 日期 - 执行日期（当重复类型为每年、每月或每日时使用）
    "Day": 1,
    // 小时 - 执行小时（0-23）
    "Hour": 0,
    // 分钟 - 执行分钟（0-59）
    "Minute": 0,
    // 秒数 - 执行秒数（0-59）
    "Second": 0,
    // 星期几 - 执行星期（0=星期日，1=星期一...6=星期六）
    "DayOfWeek": 0,
    // 月中第几周 - 月中的第几周（1-5）
    "WeekOfMonth": 0
  },

  // 倒计时执行设置 - 从指定时间开始按间隔重复执行
  "CountdownExecution": {
    // 开始时间 - 倒计时执行的开始时间
    "StartDateTime": "2024-01-01T00:00:00",
    // 间隔分钟数 - 每次执行之间的间隔时间（分钟）
    "IntervalMinutes": 60
  },

  // 高级设置 - 定时执行的高级控制选项
  "AdvancedSettings": {
    // 执行模式 - 循环控制：0=无限循环，1=限制次数，2=限制时间
    "ExecutionMode": 0,
    // 最大执行次数 - 当执行模式为限制次数时的最大执行次数（0表示无限制）
    "MaxExecutions": 0,
    // 结束时间 - 当执行模式为限制时间时的结束时间
    "EndDateTime": "2024-12-31T23:59:59",
    // 出错时停止 - 是否在处理出错时停止定时任务
    "StopOnError": false,
    // 邮件通知 - 是否在任务完成或出错时发送邮件通知
    "EmailNotification": false,
    // 邮件地址 - 接收通知的邮件地址
    "EmailAddress": ""
  }
}
