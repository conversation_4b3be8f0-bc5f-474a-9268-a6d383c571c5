{
  // 页面设置配置文件 - 控制PPT页面尺寸、方向、边距等设置
  // 此文件定义了页面大小、方向、边距、缩放等页面布局参数

  // 页面宽度 - PPT页面的宽度尺寸
  "Width": 25.4,

  // 页面高度 - PPT页面的高度尺寸
  "Height": 19.05,

  // 尺寸单位 - 页面尺寸的度量单位：厘米、英寸、像素
  "Unit": "厘米",

  // 横向布局 - 是否使用横向（风景）布局，false为纵向（肖像）布局
  "IsLandscape": true,

  // 尺寸类型 - 页面尺寸的预设类型：Standard=标准，Widescreen=宽屏，Custom=自定义
  "SizeType": "Standard",

  // 宽高比 - 页面的宽高比例
  "AspectRatio": 1.3333334,

  // 保持宽高比 - 是否在调整尺寸时保持宽高比
  "MaintainAspectRatio": true,

  // 应用到所有幻灯片 - 是否将页面设置应用到PPT的所有幻灯片
  "ApplyToAllSlides": true,

  // 背景设置 - 控制PPT页面背景的设置
  "BackgroundSettings": {
    // 背景类型 - 背景的类型：None=无背景，Color=纯色背景，Gradient=渐变背景，Image=图片背景
    "BackgroundType": "None",

    // 背景颜色 - 纯色背景的颜色值（十六进制格式）
    "BackgroundColor": "#FFFFFF",

    // 渐变起始颜色 - 渐变背景的起始颜色（十六进制格式）
    "GradientStartColor": "#FFFFFF",

    // 渐变结束颜色 - 渐变背景的结束颜色（十六进制格式）
    "GradientEndColor": "#000000",

    // 渐变方向 - 渐变的方向：Horizontal=水平，Vertical=垂直，Diagonal=对角线
    "GradientDirection": "Horizontal",

    // 图片路径 - 背景图片的文件路径
    "ImagePath": "",

    // 图片填充模式 - 背景图片的填充方式：Stretch=拉伸，Tile=平铺，Center=居中，Fit=适应
    "ImageFillMode": "Stretch",

    // 图片透明度 - 背景图片的透明度（0-100，100表示完全不透明）
    "ImageTransparency": 100
  }
}
