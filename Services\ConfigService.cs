using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Text.Json;
using System.Windows.Forms;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Services
{
    /// <summary>
    /// 配置管理服务 - 支持多配置文件管理
    /// </summary>
    public class ConfigService
    {
        private static readonly Lazy<ConfigService> _instance = new Lazy<ConfigService>(() => new ConfigService());
        public static ConfigService Instance => _instance.Value;

        private readonly string _configDirectory;
        private readonly Dictionary<string, string> _configFiles;
        private AppConfig? _currentConfig;

        // 各个配置组件的缓存
        private PathSettings? _pathSettings;
        private ProcessSettings? _processSettings;
        private LogSettings? _logSettings;
        private FormSettings? _formSettings;
        private PageSetupSettings? _pageSetupSettings;
        private BackgroundSettings? _backgroundSettings;
        private ContentDeletionSettings? _contentDeletionSettings;
        private ContentReplacementSettings? _contentReplacementSettings;
        private ParagraphFormatMatchingSettings? _paragraphFormatSettings;
        private HeaderFooterSettings? _headerFooterSettings;
        private DocumentPropertiesSettings? _documentPropertiesSettings;
        private FilenameReplacementSettings? _filenameReplacementSettings;
        private PPTFormatConversionSettings? _pptFormatConversionSettings;
        private PPTFormatSettings? _pptFormatSettings;
        private ScheduleSettings? _scheduleSettings;
        private FilenameIllegalWordsSettings? _filenameIllegalWordsSettings;
        private ContentIllegalWordsSettings? _contentIllegalWordsSettings;

        private ConfigService()
        {
            _configDirectory = Path.Combine(Application.StartupPath, "Config");

            // 初始化配置文件路径映射
            _configFiles = new Dictionary<string, string>
            {
                { "App", Path.Combine(_configDirectory, "AppConfig.json") },
                { "Path", Path.Combine(_configDirectory, "PathConfig.json") },
                { "Process", Path.Combine(_configDirectory, "ProcessConfig.json") },
                { "Log", Path.Combine(_configDirectory, "LogConfig.json") },
                { "Form", Path.Combine(_configDirectory, "FormConfig.json") },
                { "PageSetup", Path.Combine(_configDirectory, "PageSetupConfig.json") },
                { "Background", Path.Combine(_configDirectory, "BackgroundConfig.json") },
                { "ContentDeletion", Path.Combine(_configDirectory, "ContentDeletionConfig.json") },
                { "ContentReplacement", Path.Combine(_configDirectory, "ContentReplacementConfig.json") },
                { "ParagraphFormat", Path.Combine(_configDirectory, "ParagraphFormatConfig.json") },
                { "HeaderFooter", Path.Combine(_configDirectory, "HeaderFooterConfig.json") },
                { "DocumentProperties", Path.Combine(_configDirectory, "DocumentPropertiesConfig.json") },
                { "FilenameReplacement", Path.Combine(_configDirectory, "FilenameReplacementConfig.json") },
                { "PPTFormatConversion", Path.Combine(_configDirectory, "PPTFormatConversionConfig.json") },
                { "PPTFormat", Path.Combine(_configDirectory, "PPTFormatConfig.json") },
                { "Schedule", Path.Combine(_configDirectory, "ScheduleConfig.json") },
                { "FilenameIllegalWords", Path.Combine(_configDirectory, "FilenameIllegalWordsConfig.json") },
                { "ContentIllegalWords", Path.Combine(_configDirectory, "ContentIllegalWordsConfig.json") }
            };

            // 确保配置目录存在
            if (!Directory.Exists(_configDirectory))
            {
                Directory.CreateDirectory(_configDirectory);
            }

            // 初始化配置文件（确保不覆盖用户现有配置）
            InitializeConfigFiles();

            // 检查是否需要从旧配置迁移
            CheckAndMigrateOldConfig();
        }

        /// <summary>
        /// 初始化配置文件（确保不覆盖用户现有配置）
        /// </summary>
        private void InitializeConfigFiles()
        {
            try
            {
                // 项目默认配置文件目录
                string projectConfigDirectory = Path.Combine(Application.StartupPath, "Config");

                // 检查每个配置文件，如果运行时配置不存在，则从项目默认配置复制
                foreach (var configFile in _configFiles)
                {
                    string runtimeConfigPath = configFile.Value;
                    string projectConfigPath = Path.Combine(projectConfigDirectory, $"{configFile.Key}Config.json");

                    // 如果运行时配置文件不存在，但项目默认配置存在，则复制
                    if (!File.Exists(runtimeConfigPath) && File.Exists(projectConfigPath))
                    {
                        try
                        {
                            File.Copy(projectConfigPath, runtimeConfigPath);
                            LogService.Instance.LogConfigChange($"已从项目默认配置创建运行时配置文件: {Path.GetFileName(runtimeConfigPath)}");
                        }
                        catch (Exception ex)
                        {
                            LogService.Instance.LogProcessError($"复制默认配置文件失败 {configFile.Key}: {ex.Message}", ex);
                        }
                    }
                    // 如果运行时配置文件不存在，且项目默认配置也不存在，则创建默认配置
                    else if (!File.Exists(runtimeConfigPath))
                    {
                        CreateDefaultConfigFile(configFile.Key, runtimeConfigPath);
                    }
                    // 如果运行时配置文件已存在，则保持不变（不覆盖用户配置）
                    else
                    {
                        LogService.Instance.LogConfigChange($"使用现有用户配置文件: {Path.GetFileName(runtimeConfigPath)}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"初始化配置文件失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 创建默认配置文件
        /// </summary>
        private void CreateDefaultConfigFile(string configKey, string filePath)
        {
            try
            {
                object defaultConfig = configKey switch
                {
                    "App" => new Dictionary<string, object> { { "FunctionEnabled", CreateDefaultFunctionEnabled() } },
                    "Path" => new PathSettings(),
                    "Process" => new ProcessSettings(),
                    "Log" => new LogSettings(),
                    "Form" => new FormSettings(),
                    "PageSetup" => new PageSetupSettings(),
                    "Background" => new BackgroundSettings(),
                    "ContentDeletion" => new ContentDeletionSettings(),
                    "ContentReplacement" => new ContentReplacementSettings(),
                    "ParagraphFormat" => new ParagraphFormatMatchingSettings(),
                    "HeaderFooter" => new HeaderFooterSettings(),
                    "DocumentProperties" => new DocumentPropertiesSettings(),
                    "FilenameReplacement" => new FilenameReplacementSettings(),
                    "PPTFormatConversion" => new PPTFormatConversionSettings(),
                    "PPTFormat" => new PPTFormatSettings(),
                    "Schedule" => new ScheduleSettings(),
                    "FilenameIllegalWords" => new FilenameIllegalWordsSettings(),
                    "ContentIllegalWords" => new ContentIllegalWordsSettings(),
                    _ => new object()
                };

                string json = JsonSerializer.Serialize(defaultConfig, GetJsonOptions());
                File.WriteAllText(filePath, json);
                LogService.Instance.LogConfigChange($"已创建默认配置文件: {Path.GetFileName(filePath)}");
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"创建默认配置文件失败 {configKey}: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 检查并迁移旧配置文件
        /// </summary>
        private void CheckAndMigrateOldConfig()
        {
            string oldConfigFile = Path.Combine(_configDirectory, "AppConfig.json");

            // 如果存在旧的单一配置文件且新的分割配置文件不存在，则进行迁移
            if (File.Exists(oldConfigFile) && !File.Exists(_configFiles["Path"]))
            {
                try
                {
                    // 读取旧配置
                    string json = File.ReadAllText(oldConfigFile);
                    var oldConfig = JsonSerializer.Deserialize<AppConfig>(json, GetJsonOptions());

                    if (oldConfig != null)
                    {
                        // 迁移到新的分割配置文件
                        MigrateToSeparateConfigs(oldConfig);

                        // 备份旧配置文件
                        string backupFile = Path.Combine(_configDirectory, $"AppConfig_backup_{DateTime.Now:yyyyMMdd_HHmmss}.json");
                        File.Move(oldConfigFile, backupFile);

                        LogService.Instance.LogConfigChange("已成功迁移配置文件到新的分割结构");
                    }
                }
                catch (Exception ex)
                {
                    LogService.Instance.LogProcessError($"配置文件迁移失败: {ex.Message}", ex);
                }
            }
        }

        /// <summary>
        /// 迁移到分离的配置文件
        /// </summary>
        private void MigrateToSeparateConfigs(AppConfig oldConfig)
        {
            // 保存各个配置组件到独立文件
            SaveConfigComponent("Path", oldConfig.PathSettings);
            SaveConfigComponent("Process", oldConfig.ProcessSettings);
            SaveConfigComponent("Log", oldConfig.LogSettings);
            SaveConfigComponent("Form", oldConfig.FormSettings);
            SaveConfigComponent("PageSetup", oldConfig.PageSetupSettings);
            SaveConfigComponent("Background", oldConfig.BackgroundSettings);
            SaveConfigComponent("ContentDeletion", oldConfig.ContentDeletionSettings);
            SaveConfigComponent("ContentReplacement", oldConfig.ContentReplacementSettings);
            SaveConfigComponent("ParagraphFormat", oldConfig.ParagraphFormatMatchingSettings);
            SaveConfigComponent("HeaderFooter", oldConfig.HeaderFooterSettings);
            SaveConfigComponent("DocumentProperties", oldConfig.DocumentPropertiesSettings);
            SaveConfigComponent("FilenameReplacement", oldConfig.FilenameReplacementSettings);
            SaveConfigComponent("PPTFormatConversion", oldConfig.PPTFormatConversionSettings);

            // 如果旧配置中有PPT格式设置，也进行迁移
            if (oldConfig.PPTFormatSettings != null)
            {
                SaveConfigComponent("PPTFormat", oldConfig.PPTFormatSettings);
            }

            // 保存主配置（只包含功能启用状态）
            var mainConfig = new Dictionary<string, object>
            {
                { "FunctionEnabled", oldConfig.FunctionEnabled }
            };
            SaveConfigComponent("App", mainConfig);
        }

        /// <summary>
        /// 获取当前配置（兼容性方法）
        /// </summary>
        public AppConfig GetConfig()
        {
            if (_currentConfig == null)
            {
                LoadConfig();
            }
            return _currentConfig!;
        }

        /// <summary>
        /// 加载配置（兼容性方法）
        /// </summary>
        public void LoadConfig()
        {
            // 构建完整的AppConfig对象
            _currentConfig = new AppConfig
            {
                PathSettings = GetPathSettings(),
                ProcessSettings = GetProcessSettings(),
                LogSettings = GetLogSettings(),
                FormSettings = GetFormSettings(),
                PageSetupSettings = GetPageSetupSettings(),
                BackgroundSettings = GetBackgroundSettings(),
                ContentDeletionSettings = GetContentDeletionSettings(),
                ContentReplacementSettings = GetContentReplacementSettings(),
                ParagraphFormatMatchingSettings = GetParagraphFormatSettings(),
                HeaderFooterSettings = GetHeaderFooterSettings(),
                DocumentPropertiesSettings = GetDocumentPropertiesSettings(),
                FilenameReplacementSettings = GetFilenameReplacementSettings(),
                PPTFormatConversionSettings = GetPPTFormatConversionSettings(),
                PPTFormatSettings = GetPPTFormatSettings(),
                FunctionEnabled = GetFunctionEnabled()
            };
        }

        #region 通用配置组件方法

        /// <summary>
        /// 加载配置组件
        /// </summary>
        private T LoadConfigComponent<T>(string configKey) where T : new()
        {
            try
            {
                // 从运行时配置目录加载（初始化时已确保文件存在）
                if (_configFiles.TryGetValue(configKey, out string? filePath) && File.Exists(filePath))
                {
                    string json = File.ReadAllText(filePath);
                    var config = JsonSerializer.Deserialize<T>(json, GetJsonOptions());
                    if (config != null)
                    {
                        return config;
                    }
                }

                // 如果仍然加载失败，返回默认对象
                LogService.Instance.LogProcessError($"配置文件{configKey}加载失败，使用默认配置", null);
                return new T();
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"加载{configKey}配置失败: {ex.Message}", ex);
                // 出错时返回默认对象
                return new T();
            }
        }

        /// <summary>
        /// 从项目默认配置目录加载配置组件
        /// </summary>
        private T LoadDefaultConfigComponent<T>(string configKey) where T : new()
        {
            try
            {
                string defaultConfigPath = Path.Combine(Application.StartupPath, "Config", $"{configKey}Config.json");

                if (File.Exists(defaultConfigPath))
                {
                    string json = File.ReadAllText(defaultConfigPath);
                    var config = JsonSerializer.Deserialize<T>(json, GetJsonOptions());
                    if (config != null)
                    {
                        return config;
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"加载{configKey}默认配置失败: {ex.Message}", ex);
            }

            // 如果默认配置也加载失败，返回空对象
            return new T();
        }

        /// <summary>
        /// 保存配置组件
        /// </summary>
        private void SaveConfigComponent<T>(string configKey, T config)
        {
            try
            {
                if (_configFiles.TryGetValue(configKey, out string? filePath))
                {
                    string json = JsonSerializer.Serialize(config, GetJsonOptions());
                    File.WriteAllText(filePath, json);
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"保存{configKey}配置失败: {ex.Message}", ex);
            }
        }

        #endregion

        #region 各配置组件的获取方法

        /// <summary>
        /// 获取路径设置
        /// </summary>
        public PathSettings GetPathSettings()
        {
            if (_pathSettings == null)
            {
                _pathSettings = LoadConfigComponent<PathSettings>("Path");
            }
            return _pathSettings;
        }

        /// <summary>
        /// 获取处理设置
        /// </summary>
        public ProcessSettings GetProcessSettings()
        {
            if (_processSettings == null)
            {
                _processSettings = LoadConfigComponent<ProcessSettings>("Process");
            }
            return _processSettings;
        }

        /// <summary>
        /// 获取日志设置
        /// </summary>
        public LogSettings GetLogSettings()
        {
            if (_logSettings == null)
            {
                _logSettings = LoadConfigComponent<LogSettings>("Log");
            }
            return _logSettings;
        }

        /// <summary>
        /// 获取窗体设置
        /// </summary>
        public FormSettings GetFormSettings()
        {
            if (_formSettings == null)
            {
                _formSettings = LoadConfigComponent<FormSettings>("Form");
            }
            return _formSettings;
        }

        /// <summary>
        /// 获取页面设置
        /// </summary>
        public PageSetupSettings GetPageSetupSettings()
        {
            if (_pageSetupSettings == null)
            {
                _pageSetupSettings = LoadConfigComponent<PageSetupSettings>("PageSetup");
            }
            return _pageSetupSettings;
        }

        /// <summary>
        /// 获取背景设置
        /// </summary>
        public BackgroundSettings GetBackgroundSettings()
        {
            if (_backgroundSettings == null)
            {
                _backgroundSettings = LoadConfigComponent<BackgroundSettings>("Background");
            }
            return _backgroundSettings;
        }

        /// <summary>
        /// 获取内容删除设置
        /// </summary>
        public ContentDeletionSettings GetContentDeletionSettings()
        {
            if (_contentDeletionSettings == null)
            {
                _contentDeletionSettings = LoadConfigComponent<ContentDeletionSettings>("ContentDeletion");
            }
            return _contentDeletionSettings;
        }

        /// <summary>
        /// 获取内容替换设置
        /// </summary>
        public ContentReplacementSettings GetContentReplacementSettings()
        {
            if (_contentReplacementSettings == null)
            {
                _contentReplacementSettings = LoadConfigComponent<ContentReplacementSettings>("ContentReplacement");
            }
            return _contentReplacementSettings;
        }

        /// <summary>
        /// 获取段落格式设置
        /// </summary>
        public ParagraphFormatMatchingSettings GetParagraphFormatSettings()
        {
            if (_paragraphFormatSettings == null)
            {
                _paragraphFormatSettings = LoadConfigComponent<ParagraphFormatMatchingSettings>("ParagraphFormat");
            }
            return _paragraphFormatSettings;
        }

        /// <summary>
        /// 获取页眉页脚设置
        /// </summary>
        public HeaderFooterSettings GetHeaderFooterSettings()
        {
            if (_headerFooterSettings == null)
            {
                _headerFooterSettings = LoadConfigComponent<HeaderFooterSettings>("HeaderFooter");
            }
            return _headerFooterSettings;
        }

        /// <summary>
        /// 获取文档属性设置
        /// </summary>
        public DocumentPropertiesSettings GetDocumentPropertiesSettings()
        {
            if (_documentPropertiesSettings == null)
            {
                _documentPropertiesSettings = LoadConfigComponent<DocumentPropertiesSettings>("DocumentProperties");
            }
            return _documentPropertiesSettings;
        }

        /// <summary>
        /// 获取文件名替换设置
        /// </summary>
        public FilenameReplacementSettings GetFilenameReplacementSettings()
        {
            if (_filenameReplacementSettings == null)
            {
                _filenameReplacementSettings = LoadConfigComponent<FilenameReplacementSettings>("FilenameReplacement");
            }
            return _filenameReplacementSettings;
        }

        /// <summary>
        /// 获取PPT格式转换设置
        /// </summary>
        public PPTFormatConversionSettings GetPPTFormatConversionSettings()
        {
            if (_pptFormatConversionSettings == null)
            {
                _pptFormatConversionSettings = LoadConfigComponent<PPTFormatConversionSettings>("PPTFormatConversion");
            }
            return _pptFormatConversionSettings;
        }

        /// <summary>
        /// 获取PPT格式设置
        /// </summary>
        public PPTFormatSettings GetPPTFormatSettings()
        {
            if (_pptFormatSettings == null)
            {
                _pptFormatSettings = LoadConfigComponent<PPTFormatSettings>("PPTFormat");
            }
            return _pptFormatSettings;
        }

        /// <summary>
        /// 获取定时设置
        /// </summary>
        public ScheduleSettings GetScheduleSettings()
        {
            if (_scheduleSettings == null)
            {
                _scheduleSettings = LoadConfigComponent<ScheduleSettings>("Schedule");
            }
            return _scheduleSettings;
        }

        /// <summary>
        /// 获取文件名非法词设置
        /// </summary>
        public FilenameIllegalWordsSettings GetFilenameIllegalWordsSettings()
        {
            if (_filenameIllegalWordsSettings == null)
            {
                _filenameIllegalWordsSettings = LoadConfigComponent<FilenameIllegalWordsSettings>("FilenameIllegalWords");
            }
            return _filenameIllegalWordsSettings;
        }

        /// <summary>
        /// 获取内容非法词设置
        /// </summary>
        public ContentIllegalWordsSettings GetContentIllegalWordsSettings()
        {
            if (_contentIllegalWordsSettings == null)
            {
                _contentIllegalWordsSettings = LoadConfigComponent<ContentIllegalWordsSettings>("ContentIllegalWords");
            }
            return _contentIllegalWordsSettings;
        }

        /// <summary>
        /// 获取功能启用状态
        /// </summary>
        public Dictionary<string, bool> GetFunctionEnabled()
        {
            try
            {
                // 从运行时配置加载（初始化时已确保文件存在）
                if (_configFiles.TryGetValue("App", out string? filePath) && File.Exists(filePath))
                {
                    string json = File.ReadAllText(filePath);
                    var appConfig = JsonSerializer.Deserialize<Dictionary<string, object>>(json, GetJsonOptions());

                    if (appConfig != null && appConfig.TryGetValue("FunctionEnabled", out object? functionEnabledObj))
                    {
                        var functionEnabledJson = JsonSerializer.Serialize(functionEnabledObj, GetJsonOptions());
                        var functionEnabled = JsonSerializer.Deserialize<Dictionary<string, bool>>(functionEnabledJson, GetJsonOptions());
                        if (functionEnabled != null)
                        {
                            return functionEnabled;
                        }
                    }
                }

                // 如果仍然加载失败，返回默认配置
                LogService.Instance.LogProcessError("功能启用状态加载失败，使用默认配置", null);
                return CreateDefaultFunctionEnabled();
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"加载功能启用状态失败: {ex.Message}", ex);
                return CreateDefaultFunctionEnabled();
            }
        }

        /// <summary>
        /// 从项目默认配置加载功能启用状态
        /// </summary>
        private Dictionary<string, bool> LoadDefaultFunctionEnabled()
        {
            try
            {
                string defaultConfigPath = Path.Combine(Application.StartupPath, "Config", "AppConfig.json");

                if (File.Exists(defaultConfigPath))
                {
                    string json = File.ReadAllText(defaultConfigPath);
                    var appConfig = JsonSerializer.Deserialize<Dictionary<string, object>>(json, GetJsonOptions());

                    if (appConfig != null && appConfig.TryGetValue("FunctionEnabled", out object? functionEnabledObj))
                    {
                        var functionEnabledJson = JsonSerializer.Serialize(functionEnabledObj, GetJsonOptions());
                        var functionEnabled = JsonSerializer.Deserialize<Dictionary<string, bool>>(functionEnabledJson, GetJsonOptions());
                        if (functionEnabled != null)
                        {
                            return functionEnabled;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"加载默认功能启用状态失败: {ex.Message}", ex);
            }

            // 如果默认配置也加载失败，使用硬编码的默认值
            return CreateDefaultFunctionEnabled();
        }

        #endregion

        #region 各配置组件的保存方法

        /// <summary>
        /// 保存路径设置
        /// </summary>
        public void SavePathSettings(PathSettings settings)
        {
            _pathSettings = settings;
            SaveConfigComponent("Path", settings);
            // 更新缓存的完整配置
            if (_currentConfig != null)
            {
                _currentConfig.PathSettings = settings;
            }
        }

        /// <summary>
        /// 保存处理设置
        /// </summary>
        public void SaveProcessSettings(ProcessSettings settings)
        {
            _processSettings = settings;
            SaveConfigComponent("Process", settings);
            if (_currentConfig != null)
            {
                _currentConfig.ProcessSettings = settings;
            }
        }

        /// <summary>
        /// 保存日志设置
        /// </summary>
        public void SaveLogSettings(LogSettings settings)
        {
            _logSettings = settings;
            SaveConfigComponent("Log", settings);
            if (_currentConfig != null)
            {
                _currentConfig.LogSettings = settings;
            }
        }

        /// <summary>
        /// 保存窗体设置
        /// </summary>
        public void SaveFormSettings(FormSettings settings)
        {
            _formSettings = settings;
            SaveConfigComponent("Form", settings);
            if (_currentConfig != null)
            {
                _currentConfig.FormSettings = settings;
            }
        }

        /// <summary>
        /// 保存页面设置
        /// </summary>
        public void SavePageSetupSettings(PageSetupSettings settings)
        {
            _pageSetupSettings = settings;
            SaveConfigComponent("PageSetup", settings);
            if (_currentConfig != null)
            {
                _currentConfig.PageSetupSettings = settings;
            }
        }

        /// <summary>
        /// 保存背景设置
        /// </summary>
        public void SaveBackgroundSettings(BackgroundSettings settings)
        {
            _backgroundSettings = settings;
            SaveConfigComponent("Background", settings);
            if (_currentConfig != null)
            {
                _currentConfig.BackgroundSettings = settings;
            }
        }

        /// <summary>
        /// 保存内容删除设置
        /// </summary>
        public void SaveContentDeletionSettings(ContentDeletionSettings settings)
        {
            _contentDeletionSettings = settings;
            SaveConfigComponent("ContentDeletion", settings);
            if (_currentConfig != null)
            {
                _currentConfig.ContentDeletionSettings = settings;
            }
        }

        /// <summary>
        /// 保存内容替换设置
        /// </summary>
        public void SaveContentReplacementSettings(ContentReplacementSettings settings)
        {
            _contentReplacementSettings = settings;
            SaveConfigComponent("ContentReplacement", settings);
            if (_currentConfig != null)
            {
                _currentConfig.ContentReplacementSettings = settings;
            }
        }

        /// <summary>
        /// 保存段落格式设置
        /// </summary>
        public void SaveParagraphFormatSettings(ParagraphFormatMatchingSettings settings)
        {
            _paragraphFormatSettings = settings;
            SaveConfigComponent("ParagraphFormat", settings);
            if (_currentConfig != null)
            {
                _currentConfig.ParagraphFormatMatchingSettings = settings;
            }
        }

        /// <summary>
        /// 保存页眉页脚设置
        /// </summary>
        public void SaveHeaderFooterSettings(HeaderFooterSettings settings)
        {
            _headerFooterSettings = settings;
            SaveConfigComponent("HeaderFooter", settings);
            if (_currentConfig != null)
            {
                _currentConfig.HeaderFooterSettings = settings;
            }
        }

        /// <summary>
        /// 保存文档属性设置
        /// </summary>
        public void SaveDocumentPropertiesSettings(DocumentPropertiesSettings settings)
        {
            _documentPropertiesSettings = settings;
            SaveConfigComponent("DocumentProperties", settings);
            if (_currentConfig != null)
            {
                _currentConfig.DocumentPropertiesSettings = settings;
            }
        }

        /// <summary>
        /// 保存文件名替换设置
        /// </summary>
        public void SaveFilenameReplacementSettings(FilenameReplacementSettings settings)
        {
            _filenameReplacementSettings = settings;
            SaveConfigComponent("FilenameReplacement", settings);
            if (_currentConfig != null)
            {
                _currentConfig.FilenameReplacementSettings = settings;
            }
        }

        /// <summary>
        /// 保存PPT格式转换设置
        /// </summary>
        public void SavePPTFormatConversionSettings(PPTFormatConversionSettings settings)
        {
            _pptFormatConversionSettings = settings;
            SaveConfigComponent("PPTFormatConversion", settings);
            if (_currentConfig != null)
            {
                _currentConfig.PPTFormatConversionSettings = settings;
            }
        }

        /// <summary>
        /// 保存定时设置
        /// </summary>
        public void SaveScheduleSettings(ScheduleSettings settings)
        {
            _scheduleSettings = settings;
            SaveConfigComponent("Schedule", settings);
        }

        /// <summary>
        /// 保存功能启用状态
        /// </summary>
        public void SaveFunctionEnabled(Dictionary<string, bool> functionEnabled)
        {
            var appConfig = new Dictionary<string, object>
            {
                { "FunctionEnabled", functionEnabled }
            };
            SaveConfigComponent("App", appConfig);
            if (_currentConfig != null)
            {
                _currentConfig.FunctionEnabled = functionEnabled;
            }
        }

        /// <summary>
        /// 保存文件名非法词设置
        /// </summary>
        public void SaveFilenameIllegalWordsSettings(FilenameIllegalWordsSettings settings)
        {
            _filenameIllegalWordsSettings = settings;
            SaveConfigComponent("FilenameIllegalWords", settings);
        }

        /// <summary>
        /// 保存内容非法词设置
        /// </summary>
        public void SaveContentIllegalWordsSettings(ContentIllegalWordsSettings settings)
        {
            _contentIllegalWordsSettings = settings;
            SaveConfigComponent("ContentIllegalWords", settings);
        }

        #endregion

        #region 兼容性方法

        /// <summary>
        /// 保存配置（兼容性方法）
        /// </summary>
        public void SaveConfig()
        {
            if (_currentConfig != null)
            {
                SavePathSettings(_currentConfig.PathSettings);
                SaveProcessSettings(_currentConfig.ProcessSettings);
                SaveLogSettings(_currentConfig.LogSettings);
                SaveFormSettings(_currentConfig.FormSettings);
                SavePageSetupSettings(_currentConfig.PageSetupSettings);
                SaveBackgroundSettings(_currentConfig.BackgroundSettings);
                SaveContentDeletionSettings(_currentConfig.ContentDeletionSettings);
                SaveContentReplacementSettings(_currentConfig.ContentReplacementSettings);
                SaveParagraphFormatSettings(_currentConfig.ParagraphFormatMatchingSettings);
                SaveHeaderFooterSettings(_currentConfig.HeaderFooterSettings);
                SaveDocumentPropertiesSettings(_currentConfig.DocumentPropertiesSettings);
                SaveFilenameReplacementSettings(_currentConfig.FilenameReplacementSettings);
                SavePPTFormatConversionSettings(_currentConfig.PPTFormatConversionSettings);

                // 保存PPT格式设置（如果存在）
                if (_currentConfig.PPTFormatSettings != null)
                {
                    SavePPTFormatSettings(_currentConfig.PPTFormatSettings);
                }

                SaveFunctionEnabled(_currentConfig.FunctionEnabled);

                // 保存定时设置（如果存在）
                if (_currentConfig.ScheduleSettings != null)
                {
                    SaveScheduleSettings(_currentConfig.ScheduleSettings);
                }
            }
        }

        /// <summary>
        /// 更新配置（兼容性方法）
        /// </summary>
        public void UpdateConfig(AppConfig config)
        {
            _currentConfig = config;
            SaveConfig();
        }

        /// <summary>
        /// 更新文件名替换设置（兼容性方法）
        /// </summary>
        public void UpdateFilenameReplacementSettings(FilenameReplacementSettings settings)
        {
            SaveFilenameReplacementSettings(settings);
        }

        /// <summary>
        /// 保存PPT格式设置
        /// </summary>
        public void SavePPTFormatSettings(PPTFormatSettings settings)
        {
            _pptFormatSettings = settings;
            SaveConfigComponent("PPTFormat", settings);
            if (_currentConfig != null)
            {
                _currentConfig.PPTFormatSettings = settings;
            }
        }

        /// <summary>
        /// 更新PPT格式设置（兼容性方法）
        /// </summary>
        public void UpdatePPTFormatSettings(PPTFormatSettings settings)
        {
            SavePPTFormatSettings(settings);
        }

        #endregion

        /// <summary>
        /// 创建默认功能启用状态
        /// </summary>
        private Dictionary<string, bool> CreateDefaultFunctionEnabled()
        {
            var functionEnabled = new Dictionary<string, bool>();
            string[] functionNames = {
                "页面设置", "内容删除设置", "内容替换设置",
                "PPT格式设置", "匹配段落格式", "页眉页脚设置",
                "文档属性", "文件名替换", "PPT格式转换"
            };

            foreach (string functionName in functionNames)
            {
                functionEnabled[functionName] = false;
            }

            return functionEnabled;
        }

        /// <summary>
        /// 导出配置
        /// </summary>
        public bool ExportConfig(string exportPath)
        {
            try
            {
                // 创建临时导出目录
                string tempDir = Path.Combine(Path.GetTempPath(), $"PPT批量处理配置_{DateTime.Now:yyyyMMdd_HHmmss}");
                Directory.CreateDirectory(tempDir);

                try
                {
                    // 复制所有配置文件到临时目录
                    foreach (string file in Directory.GetFiles(_configDirectory, "*.json"))
                    {
                        string fileName = Path.GetFileName(file);
                        string destFile = Path.Combine(tempDir, fileName);
                        File.Copy(file, destFile, true);
                    }

                    // 创建说明文件
                    string readmeFile = Path.Combine(tempDir, "说明.txt");
                    File.WriteAllText(readmeFile, $"PPT批量处理工具配置文件\n导出时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n\n使用方法：\n1. 解压此压缩包\n2. 使用软件的\"导入配置\"功能导入解压后的文件夹\n3. 或者将解压后的所有.json文件复制到软件的Config目录下并重启软件");

                    // 创建压缩包
                    string zipFileName = $"PPT批量处理配置_{DateTime.Now:yyyyMMdd_HHmmss}.zip";
                    string zipFilePath = Path.Combine(exportPath, zipFileName);

                    // 如果文件已存在，删除它
                    if (File.Exists(zipFilePath))
                    {
                        File.Delete(zipFilePath);
                    }

                    // 创建压缩包
                    ZipFile.CreateFromDirectory(tempDir, zipFilePath);

                    return true;
                }
                finally
                {
                    // 清理临时目录
                    if (Directory.Exists(tempDir))
                    {
                        Directory.Delete(tempDir, true);
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"配置导出失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 导入配置
        /// </summary>
        public bool ImportConfig(string importPath)
        {
            try
            {
                if (Directory.Exists(importPath))
                {
                    // 从目录导入配置
                    return ImportFromDirectory(importPath);
                }
                else if (File.Exists(importPath))
                {
                    string extension = Path.GetExtension(importPath).ToLower();
                    if (extension == ".zip")
                    {
                        // 从压缩包导入配置
                        return ImportFromZipFile(importPath);
                    }
                    else if (extension == ".json")
                    {
                        // 单个配置文件导入
                        return ImportSingleJsonFile(importPath);
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"配置导入失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 从目录导入配置
        /// </summary>
        private bool ImportFromDirectory(string importPath)
        {
            try
            {
                // 备份当前配置
                BackupCurrentConfig();

                // 导入新配置
                foreach (string file in Directory.GetFiles(importPath, "*.json"))
                {
                    string fileName = Path.GetFileName(file);
                    string destFile = Path.Combine(_configDirectory, fileName);
                    File.Copy(file, destFile, true);

                    // 清除相关缓存
                    ClearConfigCache(fileName);
                }

                // 重新加载配置
                LoadConfig();
                return true;
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"从目录导入配置失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 从压缩包导入配置
        /// </summary>
        private bool ImportFromZipFile(string zipFilePath)
        {
            try
            {
                // 创建临时解压目录
                string tempDir = Path.Combine(Path.GetTempPath(), $"PPT配置导入_{DateTime.Now:yyyyMMdd_HHmmss}");
                Directory.CreateDirectory(tempDir);

                try
                {
                    // 解压压缩包
                    ZipFile.ExtractToDirectory(zipFilePath, tempDir);

                    // 从解压目录导入配置
                    return ImportFromDirectory(tempDir);
                }
                finally
                {
                    // 清理临时目录
                    if (Directory.Exists(tempDir))
                    {
                        Directory.Delete(tempDir, true);
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"从压缩包导入配置失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 导入单个JSON文件
        /// </summary>
        private bool ImportSingleJsonFile(string jsonFilePath)
        {
            try
            {
                // 备份当前配置
                BackupCurrentConfig();

                string fileName = Path.GetFileName(jsonFilePath);
                string destFile = Path.Combine(_configDirectory, fileName);
                File.Copy(jsonFilePath, destFile, true);

                // 清除相关缓存，强制重新加载
                ClearConfigCache(fileName);

                // 如果是主配置文件，重新加载完整配置
                if (fileName == "AppConfig.json")
                {
                    LoadConfig();
                }
                return true;
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"导入单个JSON文件失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 清除配置缓存
        /// </summary>
        private void ClearConfigCache(string fileName)
        {
            switch (fileName)
            {
                case "PathConfig.json":
                    _pathSettings = null;
                    break;
                case "ProcessConfig.json":
                    _processSettings = null;
                    break;
                case "LogConfig.json":
                    _logSettings = null;
                    break;
                case "FormConfig.json":
                    _formSettings = null;
                    break;
                case "PageSetupConfig.json":
                    _pageSetupSettings = null;
                    break;
                case "BackgroundConfig.json":
                    _backgroundSettings = null;
                    break;
                case "ContentDeletionConfig.json":
                    _contentDeletionSettings = null;
                    break;
                case "ContentReplacementConfig.json":
                    _contentReplacementSettings = null;
                    break;
                case "ParagraphFormatConfig.json":
                    _paragraphFormatSettings = null;
                    break;
                case "HeaderFooterConfig.json":
                    _headerFooterSettings = null;
                    break;
                case "DocumentPropertiesConfig.json":
                    _documentPropertiesSettings = null;
                    break;
                case "FilenameReplacementConfig.json":
                    _filenameReplacementSettings = null;
                    break;
                case "PPTFormatConversionConfig.json":
                    _pptFormatConversionSettings = null;
                    break;
                case "ScheduleConfig.json":
                    _scheduleSettings = null;
                    break;
                case "FilenameIllegalWordsConfig.json":
                    _filenameIllegalWordsSettings = null;
                    break;
                case "ContentIllegalWordsConfig.json":
                    _contentIllegalWordsSettings = null;
                    break;
                case "AppConfig.json":
                    // 清除所有缓存
                    _currentConfig = null;
                    _pathSettings = null;
                    _processSettings = null;
                    _logSettings = null;
                    _formSettings = null;
                    _pageSetupSettings = null;
                    _backgroundSettings = null;
                    _contentDeletionSettings = null;
                    _contentReplacementSettings = null;
                    _paragraphFormatSettings = null;
                    _headerFooterSettings = null;
                    _documentPropertiesSettings = null;
                    _filenameReplacementSettings = null;
                    _pptFormatConversionSettings = null;
                    _scheduleSettings = null;
                    _filenameIllegalWordsSettings = null;
                    _contentIllegalWordsSettings = null;
                    break;
            }
        }

        /// <summary>
        /// 备份当前配置
        /// </summary>
        private void BackupCurrentConfig()
        {
            try
            {
                string backupDir = Path.Combine(_configDirectory, "Backup");
                if (!Directory.Exists(backupDir))
                {
                    Directory.CreateDirectory(backupDir);
                }

                string backupSubDir = Path.Combine(backupDir, DateTime.Now.ToString("yyyyMMdd_HHmmss"));
                Directory.CreateDirectory(backupSubDir);

                foreach (string file in Directory.GetFiles(_configDirectory, "*.json"))
                {
                    string fileName = Path.GetFileName(file);
                    string destFile = Path.Combine(backupSubDir, fileName);
                    File.Copy(file, destFile, true);
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"配置备份失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 创建默认配置
        /// </summary>
        private AppConfig CreateDefaultConfig()
        {
            var config = new AppConfig();

            // 初始化功能启用状态
            string[] functionNames = {
                "页面设置", "内容删除设置", "内容替换设置",
                "PPT格式设置", "匹配段落格式", "页眉页脚设置",
                "文档属性", "文件名替换", "PPT格式转换"
            };

            foreach (string functionName in functionNames)
            {
                config.FunctionEnabled[functionName] = false;
            }

            return config;
        }

        /// <summary>
        /// 获取JSON序列化选项
        /// </summary>
        private JsonSerializerOptions GetJsonOptions()
        {
            return new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = null, // 保持原始属性名，避免camelCase转换问题
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
                PropertyNameCaseInsensitive = true, // 允许大小写不敏感的属性匹配
                AllowTrailingCommas = true, // 允许尾随逗号
                ReadCommentHandling = JsonCommentHandling.Skip // 跳过注释
            };
        }

        /// <summary>
        /// 重置所有配置文件为默认值
        /// </summary>
        public bool ResetAllConfigs()
        {
            try
            {
                LogService.Instance.LogConfigChange("开始重置所有配置文件为默认值");

                // 备份当前配置
                BackupCurrentConfig();

                // 清除所有缓存
                ClearAllCache();

                // 重新创建所有配置文件
                foreach (var configFile in _configFiles)
                {
                    string configKey = configFile.Key;
                    string filePath = configFile.Value;

                    try
                    {
                        // 删除现有配置文件
                        if (File.Exists(filePath))
                        {
                            File.Delete(filePath);
                        }

                        // 创建默认配置文件
                        CreateDefaultConfigFile(configKey, filePath);
                    }
                    catch (Exception ex)
                    {
                        LogService.Instance.LogProcessError($"重置配置文件失败 {configKey}: {ex.Message}", ex);
                        return false;
                    }
                }

                // 强制刷新日志，确保重置操作被记录
                LogService.Instance.LogConfigChange("所有配置文件已成功重置为默认值，软件即将关闭");
                LogService.Instance.Dispose(); // 立即刷新日志缓冲区

                return true;
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"重置配置失败: {ex.Message}", ex);
                LogService.Instance.Dispose(); // 确保错误日志被写入
                return false;
            }
        }

        /// <summary>
        /// 清除所有配置缓存
        /// </summary>
        private void ClearAllCache()
        {
            _currentConfig = null;
            _pathSettings = null;
            _processSettings = null;
            _logSettings = null;
            _formSettings = null;
            _pageSetupSettings = null;
            _backgroundSettings = null;
            _contentDeletionSettings = null;
            _contentReplacementSettings = null;
            _paragraphFormatSettings = null;
            _headerFooterSettings = null;
            _documentPropertiesSettings = null;
            _filenameReplacementSettings = null;
            _pptFormatConversionSettings = null;
            _scheduleSettings = null;
            _filenameIllegalWordsSettings = null;
            _contentIllegalWordsSettings = null;
        }

    }
}
