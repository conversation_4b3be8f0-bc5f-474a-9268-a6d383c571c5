{
  // 内容删除配置文件 - 控制PPT内容删除功能的详细设置
  // 此文件包含11个删除类别：文档删除、内容删除、文本删除、图片删除、表格删除、图表删除、媒体删除、联系方式删除、动画删除、备注删除、格式删除

  // 文档删除设置 - 根据文件属性条件删除整个PPT文档
  "DocumentDeletion": {
    // 启用文档删除功能开关 - 是否根据文件条件删除整个PPT文档
    "EnableDocumentDeletion": false,

    // 删除范围 - 指定删除操作的作用范围（1=普通幻灯片页，2=母版，4=版式页，可组合使用）
    "DeletionScope": 1,

    // 启用文件名长度检查 - 是否根据文件名长度删除文档
    "EnableFileNameLengthCheck": false,
    // 文件名最小长度 - 文件名字符数下限
    "FileNameMinLength": 1,
    // 文件名最大长度 - 文件名字符数上限
    "FileNameMaxLength": 10,

    // 启用文件大小检查 - 是否根据文件大小删除文档
    "EnableFileSizeCheck": false,
    // 文件最小大小 - 文件大小下限
    "FileMinSize": 1,
    // 文件最小大小单位 - B、KB、MB、GB
    "FileMinSizeUnit": "KB",
    // 文件最大大小 - 文件大小上限
    "FileMaxSize": 999,
    // 文件最大大小单位 - B、KB、MB、GB
    "FileMaxSizeUnit": "KB",
    // 文件大小单位 - 保留用于向后兼容（建议使用上面的分别设置）
    "FileSizeUnit": "KB",

    // 启用内容字符数检查 - 是否根据PPT内容字符数删除文档
    "EnableContentCharCountCheck": false,
    // 内容最小字符数 - PPT内容字符数下限
    "ContentMinCharCount": 1,
    // 内容最大字符数 - PPT内容字符数上限
    "ContentMaxCharCount": 200,

    // 启用页面数检查 - 是否根据PPT页面数删除文档
    "EnablePageCountCheck": false,
    // 最小页面数 - PPT页面数下限
    "MinPageCount": 1,
    // 最大页面数 - PPT页面数上限
    "MaxPageCount": 3,

    // 启用文件名违禁词检查 - 是否根据文件名包含违禁词删除文档
    "EnableFileNameIllegalWordsCheck": false,
    // 文件名违禁词列表 - 包含这些词的文件将被删除
    "FileNameIllegalWords": [],

    // 启用内容违禁词检查 - 是否根据PPT内容包含违禁词删除文档
    "EnableContentIllegalWordsCheck": false,
    // 内容违禁词列表 - 包含这些词的PPT将被删除
    "ContentIllegalWords": []
  },
  // 内容删除设置 - 删除PPT中的特定内容元素
  "ContentRemoval": {
    // 启用内容删除功能开关 - 是否启用内容删除功能
    "EnableContentRemoval": false,

    // 删除范围 - 指定删除操作的作用范围（1=普通幻灯片页，2=母版，4=版式页，可组合使用）
    "DeletionScope": 1,

    // 删除空白幻灯片 - 删除没有任何内容的幻灯片
    "DeleteBlankSlides": false,
    // 删除不包含任何文字内容的页 - 删除只含图片或图形但没有文字的幻灯片
    "DeleteSlidesWithoutText": false,
    // 删除第一张幻灯片 - 删除PPT的第一张幻灯片
    "DeleteFirstSlide": false,
    // 删除最后一张幻灯片 - 删除PPT的最后一张幻灯片
    "DeleteLastSlide": false,

    // 启用幻灯片范围删除 - 是否删除指定范围的幻灯片
    "EnableSlideRangeDeletion": false,
    // 幻灯片范围起始页 - 删除范围的起始页码
    "SlideRangeStart": 1,
    // 幻灯片范围结束页 - 删除范围的结束页码
    "SlideRangeEnd": 1,

    // 启用关键词幻灯片删除 - 是否删除包含特定关键词的幻灯片
    "EnableKeywordSlidesDeletion": false,
    // 幻灯片关键词列表 - 包含这些关键词的幻灯片将被删除
    "SlideKeywords": [],

    // 启用指定图片幻灯片删除 - 是否删除包含指定图片的幻灯片
    "EnableSpecificImageSlidesDeletion": false,
    // 指定图片名称列表 - 包含这些图片的幻灯片将被删除
    "SpecificImageNames": [],

    // 删除空白段落 - 删除PPT中的空白段落
    "DeleteBlankParagraphs": false,
    // 删除空白行 - 删除PPT中的空白行
    "DeleteBlankLines": false
  },

  // 文本删除设置 - 删除PPT中的文本内容
  "TextDeletion": {
    // 启用文本删除功能开关 - 是否启用文本删除功能
    "EnableTextDeletion": false,

    // 删除范围 - 指定删除操作的作用范围（1=普通幻灯片页，2=母版，4=版式页，可组合使用）
    "DeletionScope": 1,

    // 删除所有文本 - 删除PPT中的所有文本内容
    "DeleteAllText": false,
    // 删除文本框 - 删除PPT中的文本框
    "DeleteTextBoxes": false,
    // 删除标题 - 删除PPT中的标题文本
    "DeleteTitles": false,
    // 删除项目符号 - 删除PPT中的项目符号和编号列表
    "DeleteBulletPoints": false,

    // 删除特定文本 - 删除指定的文本内容
    "DeleteSpecificText": false,
    // 特定文本内容列表 - 要删除的具体文本内容
    "SpecificTextContent": [],

    // 删除文本范围 - 删除指定幻灯片范围内的文本
    "DeleteTextRange": false,
    // 文本删除范围起始页 - 文本删除的起始页码
    "TextSlideRangeStart": 1,
    // 文本删除范围结束页 - 文本删除的结束页码
    "TextSlideRangeEnd": 10
  },
  // 图片删除设置 - 删除PPT中的图片内容
  "ImageDeletion": {
    // 启用图片删除功能开关 - 是否启用图片删除功能
    "EnableImageDeletion": false,

    // 删除范围 - 指定删除操作的作用范围（1=普通幻灯片页，2=母版，4=版式页，可组合使用）
    "DeletionScope": 1,

    // 删除所有图片 - 删除PPT中的所有图片
    "DeleteAllImages": false,

    // 启用特定图片删除 - 是否删除指定名称的图片
    "EnableSpecificImageDeletion": false,
    // 特定图片名称列表 - 要删除的具体图片名称
    "SpecificImageNames": [],

    // 启用幻灯片范围图片删除 - 是否删除指定范围内的图片
    "EnableSlideRangeImageDeletion": false,
    // 图片删除范围起始页 - 图片删除的起始页码
    "ImageSlideRangeStart": 1,
    // 图片删除范围结束页 - 图片删除的结束页码
    "ImageSlideRangeEnd": 10,

    // 删除最后一张幻灯片的图片 - 删除PPT最后一页的所有图片
    "DeleteLastSlideImages": false,

    // 启用位置图片删除 - 是否根据图片位置删除图片
    "EnablePositionImageDeletion": false,
    // 位置区域列表 - 定义要删除图片的区域，支持多个区域
    "PositionRegions": [
      {
        // 区域名称 - 便于识别的区域描述
        "Name": "右上角logo区域",
        // X坐标百分比 - 区域左上角X坐标（0-100%）
        "XPercent": 85.0,
        // Y坐标百分比 - 区域左上角Y坐标（0-100%）
        "YPercent": 0.0,
        // 宽度百分比 - 区域宽度（0-100%）
        "WidthPercent": 15.0,
        // 高度百分比 - 区域高度（0-100%）
        "HeightPercent": 10.0,
        // 是否启用此区域 - 此区域是否生效
        "IsEnabled": true
      },
      {
        // 区域名称 - 便于识别的区域描述
        "Name": "底部装饰区域",
        // X坐标百分比 - 区域左上角X坐标（0-100%）
        "XPercent": 0.0,
        // Y坐标百分比 - 区域左上角Y坐标（0-100%）
        "YPercent": 90.0,
        // 宽度百分比 - 区域宽度（0-100%）
        "WidthPercent": 100.0,
        // 高度百分比 - 区域高度（0-100%）
        "HeightPercent": 10.0,
        // 是否启用此区域 - 此区域是否生效
        "IsEnabled": false
      }
    ],
    // 单一位置X坐标百分比 - 单个位置删除的X坐标（0-100%）
    "PositionXPercent": 0.0,
    // 单一位置Y坐标百分比 - 单个位置删除的Y坐标（0-100%）
    "PositionYPercent": 0.0,
    // 单一位置宽度百分比 - 单个位置删除的宽度（0-100%）
    "PositionWidthPercent": 20.0,
    // 单一位置高度百分比 - 单个位置删除的高度（0-100%）
    "PositionHeightPercent": 20.0,

    // 删除背景图片 - 删除PPT的背景图片
    "DeleteBackgroundImages": false
  },
  // 表格删除设置 - 删除PPT中的表格内容
  "TableDeletion": {
    // 启用表格删除功能开关 - 是否启用表格删除功能
    "EnableTableDeletion": false,

    // 删除范围 - 指定删除操作的作用范围（1=普通幻灯片页，2=母版，4=版式页，可组合使用）
    "DeletionScope": 1,

    // 删除所有表格 - 删除PPT中的所有表格
    "DeleteAllTables": false,
    // 删除最后一张幻灯片的表格 - 删除PPT最后一页的所有表格
    "DeleteLastSlideTables": false,
    // 启用幻灯片范围表格删除 - 是否删除指定范围内的表格
    "EnableSlideRangeTableDeletion": false,
    // 表格删除范围起始页 - 表格删除的起始页码
    "TableSlideRangeStart": 1,
    // 表格删除范围结束页 - 表格删除的结束页码
    "TableSlideRangeEnd": 10
  },

  // 图表删除设置 - 删除PPT中的图表内容
  "ChartDeletion": {
    // 启用图表删除功能开关 - 是否启用图表删除功能
    "EnableChartDeletion": false,

    // 删除范围 - 指定删除操作的作用范围（1=普通幻灯片页，2=母版，4=版式页，可组合使用）
    "DeletionScope": 1,

    // 删除所有图表 - 删除PPT中的所有图表
    "DeleteAllCharts": false,
    // 删除最后一张幻灯片的图表 - 删除PPT最后一页的所有图表
    "DeleteLastSlideCharts": false,
    // 启用幻灯片范围图表删除 - 是否删除指定范围内的图表
    "EnableSlideRangeChartDeletion": false,
    // 图表删除范围起始页 - 图表删除的起始页码
    "ChartSlideRangeStart": 1,
    // 图表删除范围结束页 - 图表删除的结束页码
    "ChartSlideRangeEnd": 10,

    // 删除包含指定文本的图表 - 是否删除包含特定关键词的图表
    "DeleteChartsWithText": false,
    // 图表文本关键词列表 - 包含这些关键词的图表将被删除
    "ChartKeywords": []
  },

  // 媒体删除设置 - 删除PPT中的音频和视频内容
  "MediaDeletion": {
    // 启用媒体删除功能开关 - 是否启用媒体删除功能
    "EnableMediaDeletion": false,

    // 删除范围 - 指定删除操作的作用范围（1=普通幻灯片页，2=母版，4=版式页，可组合使用）
    "DeletionScope": 1,

    // 删除所有音频 - 删除PPT中的所有音频文件
    "DeleteAllAudio": false,
    // 删除最后一张幻灯片的音频 - 删除PPT最后一页的所有音频
    "DeleteLastSlideAudio": false,
    // 启用幻灯片范围音频删除 - 是否删除指定范围内的音频
    "EnableSlideRangeAudioDeletion": false,
    // 音频删除范围起始页 - 音频删除的起始页码
    "AudioSlideRangeStart": 1,
    // 音频删除范围结束页 - 音频删除的结束页码
    "AudioSlideRangeEnd": 10,

    // 删除所有视频 - 删除PPT中的所有视频文件
    "DeleteAllVideo": false,
    // 删除最后一张幻灯片的视频 - 删除PPT最后一页的所有视频
    "DeleteLastSlideVideo": false,
    // 启用幻灯片范围视频删除 - 是否删除指定范围内的视频
    "EnableSlideRangeVideoDeletion": false,
    // 视频删除范围起始页 - 视频删除的起始页码
    "VideoSlideRangeStart": 1,
    // 视频删除范围结束页 - 视频删除的结束页码
    "VideoSlideRangeEnd": 10
  },

  // 联系方式删除设置 - 删除PPT中的联系信息
  "ContactDeletion": {
    // 启用联系方式删除功能开关 - 是否启用联系方式删除功能
    "EnableContactDeletion": false,

    // 删除范围 - 指定删除操作的作用范围（1=普通幻灯片页，2=母版，4=版式页，可组合使用）
    "DeletionScope": 1,

    // 删除手机号码 - 删除PPT中的手机号码
    "DeletePhoneNumbers": false,
    // 删除座机号码 - 删除PPT中的座机号码
    "DeleteLandlineNumbers": false,
    // 删除电子邮箱 - 删除PPT中的电子邮箱地址
    "DeleteEmailAddresses": false,
    // 删除网站地址 - 删除PPT中的网站URL
    "DeleteWebsites": false,
    // 删除超链接 - 删除PPT中的所有超链接
    "DeleteHyperlinks": false
  },

  // 动画删除设置 - 删除PPT中的动画效果和切换效果
  "AnimationDeletion": {
    // 启用动画删除功能开关 - 是否启用动画删除功能
    "EnableAnimationDeletion": false,

    // 删除范围 - 指定删除操作的作用范围（1=普通幻灯片页，2=母版，4=版式页，可组合使用）
    "DeletionScope": 1,

    // 动画效果删除选项
    // 删除所有动画效果 - 删除PPT中的所有动画效果
    "DeleteAllAnimations": false,
    // 删除最后一页动画效果 - 删除PPT最后一页的所有动画效果
    "DeleteLastSlideAnimations": false,
    // 启用动画范围删除 - 是否删除指定范围内的动画效果
    "EnableSlideRangeAnimationDeletion": false,
    // 动画删除起始页 - 动画删除的起始页码
    "AnimationSlideRangeStart": 1,
    // 动画删除结束页 - 动画删除的结束页码
    "AnimationSlideRangeEnd": 1,

    // 切换效果删除选项
    // 删除所有切换效果 - 删除PPT中的所有幻灯片切换效果
    "DeleteAllTransitions": false,
    // 删除最后一页切换效果 - 删除PPT最后一页的切换效果
    "DeleteLastSlideTransitions": false,
    // 启用切换效果范围删除 - 是否删除指定范围内的切换效果
    "EnableSlideRangeTransitionDeletion": false,
    // 切换效果删除起始页 - 切换效果删除的起始页码
    "TransitionSlideRangeStart": 1,
    // 切换效果删除结束页 - 切换效果删除的结束页码
    "TransitionSlideRangeEnd": 1
  },

  // 备注删除设置 - 删除PPT中的备注内容
  "NotesDeletion": {
    // 启用备注删除功能开关 - 是否启用备注删除功能
    "EnableNotesDeletion": false,

    // 删除范围 - 指定删除操作的作用范围（1=普通幻灯片页，2=母版，4=版式页，可组合使用）
    "DeletionScope": 1,

    // 删除幻灯片备注 - 删除PPT中的幻灯片备注
    "DeleteSlideNotes": false,
    // 清空备注内容 - 清空PPT中的备注内容但保留备注区域
    "ClearNotesContent": false
  },

  // 格式删除设置 - 删除PPT中的格式设置
  "FormatDeletion": {
    // 启用格式删除功能开关 - 是否启用格式删除功能
    "EnableFormatDeletion": false,

    // 删除范围 - 指定删除操作的作用范围（1=普通幻灯片页，2=母版，4=版式页，可组合使用）
    "DeletionScope": 1,

    // 文本格式删除选项
    // 删除字体格式 - 删除PPT中的字体格式设置（字体、大小、颜色等）
    "DeleteFontFormatting": false,
    // 删除段落格式 - 删除PPT中的段落格式设置（对齐、缩进、行距等）
    "DeleteParagraphFormatting": false,
    // 删除列表格式 - 删除PPT中的列表格式设置（项目符号、编号等）
    "DeleteListFormatting": false,

    // 对象格式删除选项
    // 删除表格格式 - 删除PPT中的表格格式设置（边框、填充、样式等）
    "DeleteTableFormatting": false,
    // 删除形状格式 - 删除PPT中的形状格式设置（填充、边框、效果等）
    "DeleteShapeFormatting": false,
    // 删除图片格式 - 删除PPT中的图片格式设置（边框、效果、调整等）
    "DeleteImageFormatting": false,
    // 删除图表格式 - 删除PPT中的图表格式设置（样式、颜色、效果等）
    "DeleteChartFormatting": false,

    // 背景格式删除选项
    // 删除背景格式 - 删除PPT中的背景格式设置（背景色、图片、渐变等）
    "DeleteBackgroundFormatting": false
  }
}
