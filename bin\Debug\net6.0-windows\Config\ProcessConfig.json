{
  // 处理配置文件 - 控制PPT批量处理的核心参数
  // 此文件定义了多线程处理、重试机制、支持格式等关键设置

  // 线程数量 - 同时处理PPT文件的线程数，建议根据CPU核心数设置，通常为2-8
  "ThreadCount": 4,

  // 重试次数 - 当文件处理失败时的重试次数，0表示不重试
  "RetryCount": 3,

  // 批处理大小 - 每批次处理的文件数量，影响内存使用和处理效率
  "BatchSize": 50,

  // 跳过临时文件 - 是否自动跳过以~$开头的Microsoft Office临时文件
  "SkipTemporaryFiles": true,

  // 支持的PPT格式列表 - 基于Aspose.Slides SaveFormat枚举的支持格式
  "SupportedFormats": [
    ".ppt",   // PowerPoint 97-2003 演示文稿 (SaveFormat.Ppt)
    ".pptx",  // PowerPoint 演示文稿 (SaveFormat.Pptx)
    ".pptm",  // PowerPoint 启用宏的演示文稿 (SaveFormat.Pptm)
    ".ppsx",  // PowerPoint 幻灯片放映 (SaveFormat.Ppsx)
    ".ppsm",  // PowerPoint 启用宏的幻灯片放映 (SaveFormat.Ppsm)
    ".potx",  // PowerPoint 模板 (SaveFormat.Potx)
    ".potm",  // PowerPoint 启用宏的模板 (SaveFormat.Potm)
    ".odp",   // OpenDocument 演示文稿 (SaveFormat.Odp)
    ".otp"    // OpenDocument 演示文稿模板 (SaveFormat.Otp)
  ],

  // 启用重试机制开关 - 是否在处理失败时进行重试
  "EnableRetry": true,

  // 重试延迟时间 - 重试之间的等待时间，单位为毫秒
  "RetryDelay": 1000
}
