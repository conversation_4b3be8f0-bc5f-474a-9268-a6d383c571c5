{
  // 文件名违禁词配置文件 - 控制PPT文件名中违禁词的检测和处理
  // 此文件定义了文件名违禁词过滤规则和处理方式等参数

  // 启用文件名违禁词过滤 - 是否启用PPT文件名违禁词检测功能
  "EnableFilenameIllegalWordsFilter": false,

  // 区分大小写 - 文件名违禁词匹配是否区分大小写
  "CaseSensitive": false,

  // 使用正则表达式 - 是否将违禁词作为正则表达式处理
  "UseRegex": false,

  // 匹配完整单词 - 是否只匹配完整的单词（文件名通常设为false以检测部分匹配）
  "MatchWholeWords": false,

  // 违禁词列表 - 需要检测的文件名违禁词汇，支持特殊字符和正则表达式
  "IllegalWords": [
    "示例非法词1",
    "示例非法词2",
    "包含\"引号\"的词",
    "包含\\反斜杠的词",
    "包含/斜杠的词",
    "包含<>尖括号的词",
    "包含|管道符的词",
    "包含*星号的词",
    "包含?问号的词",
    "包含:冒号的词"
  ],

  // 替换动作 - 发现违禁词时的处理方式：0=删除文件，1=重命名文件，2=跳过文件
  "ReplacementAction": 0,

  // 替换文本 - 当替换动作为1时，用于替换文件名中违禁词的文本
  "ReplacementText": "[已删除]",

  // 记录违禁词 - 是否在日志中记录发现的文件名违禁词
  "LogIllegalWords": true,

  // 配置说明 - 此配置文件的功能描述和参数说明
  "Description": "文件名非法词过滤配置。支持特殊字符，JSON会自动转义。ReplacementAction: 0=删除文件, 1=重命名文件, 2=跳过文件"
}
