{
  // 日志配置文件 - 控制程序日志记录的详细设置
  // 此文件定义了日志级别、输出方式、文件管理和分类记录等参数

  // 启用日志记录 - 是否启用程序日志功能
  "EnableLogging": true,

  // 日志级别 - 记录日志的详细程度：0=错误，1=警告，2=信息，3=调试
  "LogLevel": 1,

  // 记录到文件 - 是否将日志保存到文件
  "LogToFile": true,

  // 记录到控制台 - 是否在控制台显示日志信息
  "LogToConsole": false,

  // 最大日志文件大小 - 单个日志文件的最大大小（字节），超过后自动创建新文件
  "MaxLogFileSize": 10485760,

  // 最大日志文件数 - 保留的日志文件最大数量，超过后删除最旧的文件
  "MaxLogFiles": 10,

  // 日志目录 - 日志文件保存的目录路径
  "LogDirectory": "Logs",

  // 启用的日志类型 - 控制不同类型日志的记录开关，支持按类别分文件记录
  "EnabledLogTypes": {
    // 处理开始日志 - 记录文件处理开始的信息
    "处理开始": true,
    // 处理完成日志 - 记录文件处理完成的信息
    "处理完成": true,
    // 处理错误日志 - 记录文件处理过程中的错误信息
    "处理错误": true,
    // 文件跳过日志 - 记录被跳过文件的信息
    "文件跳过": true,
    // 配置变更日志 - 记录配置文件变更的信息
    "配置变更": true,
    // 系统信息日志 - 记录系统运行状态信息
    "系统信息": true,
    // 性能统计日志 - 记录程序性能统计信息
    "性能统计": false,
    // 调试信息日志 - 记录详细的调试信息
    "调试信息": false
  }
}
