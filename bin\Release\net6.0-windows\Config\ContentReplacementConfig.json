{
  // 内容替换配置文件 - 控制PPT内容替换功能的设置
  // 此文件定义了文本替换、形状替换、字体替换、颜色替换等4个标签页的配置，支持Excel导入和模板功能

  // 文本替换设置 - 控制PPT中文本内容的替换功能
  "TextReplacement": {
    // 启用文本替换功能开关 - 是否启用文本替换功能
    "EnableTextReplacement": false,

    // 替换规则列表 - 定义具体的文本替换规则，支持普通文本和正则表达式替换
    "ReplacementRules": [],

    // 启用普通文本替换 - 是否启用普通文本的替换功能，进行精确匹配替换
    "EnableNormalTextReplacement": true,

    // 启用正则表达式替换 - 是否启用基于正则表达式的文本替换，支持模式匹配
    "EnableRegexReplacement": false,

    // 启用批量文本替换 - 是否启用批量文本替换功能，可从Excel文件导入替换规则
    "EnableBatchTextReplacement": false,

    // 启用范围替换 - 是否启用指定幻灯片范围内的文本替换
    "EnableRangeReplacement": false,

    // 替换范围设置 - 定义文本替换应用的幻灯片范围
    "ReplacementScope": {
      // 是否在普通幻灯片页中进行替换
      "IncludeNormalSlides": true,
      // 是否在母版页中进行替换
      "IncludeMasterSlides": true,
      // 是否在母版版式页中进行替换
      "IncludeLayoutSlides": true
    },

    // 替换范围设置 - 定义文本替换的应用范围和目标区域
    "ReplacementRange": {
      // 替换标题中的文本 - 是否在幻灯片标题中进行文本替换
      "ReplaceInTitles": true,
      // 替换内容中的文本 - 是否在幻灯片正文内容中进行文本替换
      "ReplaceInContent": true,
      // 替换备注中的文本 - 是否在幻灯片备注中进行文本替换
      "ReplaceInNotes": false,
      // 替换表格中的文本 - 是否在表格单元格中进行文本替换
      "ReplaceInTables": true,
      // 替换图表中的文本 - 是否在图表标题、标签等文本中进行替换
      "ReplaceInCharts": false
    }
  },
  // 形状替换设置 - 控制PPT中形状元素的替换功能
  "ShapeReplacement": {
    // 启用形状替换功能开关 - 是否启用形状替换功能
    "EnableShapeReplacement": false,

    // 启用图片替换 - 是否启用图片的替换功能
    "EnableImageReplacement": false,
    // 图片替换规则列表 - 定义具体的图片替换规则，支持按名称、路径或属性替换图片
    "ImageReplacementRules": [],

    // 启用文本框替换 - 是否启用文本框的替换功能
    "EnableTextBoxReplacement": false,
    // 文本框替换规则列表 - 定义具体的文本框替换规则，可替换文本框样式、内容等
    "TextBoxReplacementRules": [],

    // 启用形状样式替换 - 是否启用形状样式的替换功能
    "EnableShapeStyleReplacement": false,
    // 形状样式替换规则列表 - 定义具体的形状样式替换规则，包括颜色、边框、效果等
    "ShapeStyleReplacementRules": [],

    // 替换范围设置 - 定义形状替换应用的幻灯片范围
    "ReplacementScope": {
      // 是否在普通幻灯片页中进行替换
      "IncludeNormalSlides": true,
      // 是否在母版页中进行替换
      "IncludeMasterSlides": true,
      // 是否在母版版式页中进行替换
      "IncludeLayoutSlides": true
    }
  },

  // 字体替换设置 - 控制PPT中字体的替换和嵌入功能
  "FontReplacement": {
    // 启用字体替换功能开关 - 是否启用字体替换功能
    "EnableFontReplacement": false,

    // 启用字体名称替换 - 是否启用字体名称的替换功能
    "EnableFontNameReplacement": false,
    // 字体名称替换规则列表 - 定义具体的字体名称替换规则，如将宋体替换为微软雅黑
    "FontNameReplacementRules": [],

    // 启用字体样式替换 - 是否启用字体样式的替换功能
    "EnableFontStyleReplacement": false,
    // 字体样式替换规则列表 - 定义具体的字体样式替换规则，包括粗体、斜体、下划线等
    "FontStyleReplacementRules": [],

    // 启用字体嵌入 - 是否启用字体嵌入功能，确保PPT在其他设备上正确显示
    "EnableFontEmbedding": false,
    // 要嵌入的字体列表 - 指定需要嵌入到PPT中的字体名称
    "FontsToEmbed": [],
    // 嵌入所有字体 - 是否嵌入PPT中使用的所有字体
    "EmbedAllFonts": false,
    // 仅嵌入使用的字符 - 是否只嵌入实际使用的字符以减小文件大小
    "EmbedUsedCharactersOnly": true,

    // 替换范围设置 - 定义字体替换应用的幻灯片范围
    "ReplacementScope": {
      // 是否在普通幻灯片页中进行替换
      "IncludeNormalSlides": true,
      // 是否在母版页中进行替换
      "IncludeMasterSlides": true,
      // 是否在母版版式页中进行替换
      "IncludeLayoutSlides": true
    }
  },

  // 颜色替换设置 - 控制PPT中颜色的替换功能
  "ColorReplacement": {
    // 启用颜色替换功能开关 - 是否启用颜色替换功能
    "EnableColorReplacement": false,

    // 启用主题颜色替换 - 是否启用主题颜色的替换功能
    "EnableThemeColorReplacement": false,
    // 主题颜色替换规则列表 - 定义具体的主题颜色替换规则，替换PPT主题中的预定义颜色
    "ThemeColorReplacementRules": [],

    // 启用自定义颜色替换 - 是否启用自定义颜色的替换功能
    "EnableCustomColorReplacement": false,
    // 自定义颜色替换规则列表 - 定义具体的自定义颜色替换规则，可按RGB值或颜色名称替换
    "CustomColorReplacementRules": [],

    // 替换范围设置 - 定义颜色替换应用的幻灯片范围
    "ReplacementScope": {
      // 是否在普通幻灯片页中进行替换
      "IncludeNormalSlides": true,
      // 是否在母版页中进行替换
      "IncludeMasterSlides": true,
      // 是否在母版版式页中进行替换
      "IncludeLayoutSlides": true
    }
  }
}
